<script setup lang="ts">
import Banner from './banner/index.vue'
import Search from './search/index.vue'
import Option from './option/index.vue'
import Card from './card/index.vue'

const levels:string[] = []
const regions:string[] = []
import { getHospitalList, getHospitalLevels } from '@/api/home'
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { Content, HospitalResponseData, HospitalLevenAndRegionResponseData } from '@/api/home/<USER>'

const hospitals = ref<Content>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

onMounted(() => {
  getHospitalLevelInfo()
  getHospitalInfo()
})

async function getHospitalInfo() {
  try {
    const res: HospitalResponseData = await getHospitalList(currentPage.value, pageSize.value)
    total.value = res.data.totalElements
    hospitals.value = res.data.content
  } catch (error) {
    ElMessage.error('获取医院列表失败')
  }
}

async function getHospitalLevelInfo() {
  try {
    const res: HospitalLevenAndRegionResponseData = await getHospitalLevels('HosType')
    for (const h in res.data) {
      levels.push(h.name)
    }
  } catch (error) {
    ElMessage.error('获取医院等级失败')
  }
}

</script>

<template>
  <Banner />
  <Search />
  <el-row :gutter="20">
    <el-col :span="20" class="option">
      <span>医院</span>
      <Option name="等级" :options="levels" />
      <Option name="地区" :options="regions" />
      <div class="hospital">
        <Card class="card" v-for="hospital in hospitals" :key="hospital.id" :name="hospital.hosname"
          :level="hospital.param.hostypeString" :time="hospital.bookingRule.releaseTime" :logo="hospital.logoData" />
        <el-pagination class="page" v-model:current-page="currentPage" v-model:page-size="pageSize"
          :page-sizes="[5, 10, 15, 20]" layout="prev, pager, next, ->, sizes, total" :total="total"
          @current-change="getHospitalInfo" @size-change="currentPage = 1; getHospitalInfo()" />
      </div>
    </el-col>
    <el-col :span="4">
      456
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.option {
  span {
    color: #7f7f7f;
    display: inline-block;
    margin: 10px 0;
  }
}

.hospital {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;

  .card {
    width: 48%;
  }

  .page {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
